<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useReaderStore } from '../stores/reader';

const route = useRoute();
const router = useRouter();
const readerStore = useReaderStore();

// Route params
const mangaId = computed(() => route.params.id);
const chapterId = computed(() => route.params.chapter);
const pageParam = computed(() => route.params.page ? parseInt(route.params.page) : 1);

// UI state
const showControls = ref(true);
const showSettings = ref(false);
const showChapterSelector = ref(false);
const controlsTimeout = ref(null);

// List view state
const currentPageInView = ref(1);
const pageIntersectionObserver = ref(null);

// Get data from store
const manga = computed(() => readerStore.getManga);
const chapter = computed(() => readerStore.getChapter);
const chapters = computed(() => readerStore.getChapters);
const pages = computed(() => readerStore.getPages);
const currentPage = computed(() => readerStore.getCurrentPage);
const settings = computed(() => readerStore.getSettings);
const loading = computed(() => readerStore.loading);
const error = computed(() => readerStore.error);

// Computed values
const totalPages = computed(() => readerStore.getTotalPages);
const hasNextPage = computed(() => readerStore.hasNextPage);
const hasPrevPage = computed(() => readerStore.hasPrevPage);
const hasNextChapter = computed(() => readerStore.hasNextChapter);
const hasPrevChapter = computed(() => readerStore.hasPrevChapter);

// Double-page mode computed values
const currentPagePair = computed(() => readerStore.getCurrentPagePair);
const currentPageDisplay = computed(() => readerStore.getCurrentPageDisplay);

const currentPageUrl = computed(() => {
  if (!pages.value || !pages.value.length || currentPage.value > pages.value.length) return null;
  return pages.value[currentPage.value - 1]?.url;
});

// Methods
const loadContent = async () => {
  try {
    // Load manga data
    await readerStore.fetchManga(mangaId.value);
    
    // Load chapters
    await readerStore.fetchChapters(mangaId.value);
    
    // Determine which chapter to load
    let targetChapterId = chapterId.value;
    if (!targetChapterId && chapters.value.length > 0) {
      // If no chapter specified, use the first one
      targetChapterId = chapters.value[0].id;
    }
    
    if (targetChapterId) {
      // Load chapter
      await readerStore.fetchChapter(mangaId.value, targetChapterId);
      
      // Load pages
      await readerStore.fetchPages(mangaId.value, targetChapterId);
      
      // Set initial page
      if (pageParam.value && pageParam.value <= totalPages.value) {
        readerStore.setCurrentPage(pageParam.value);
      } else {
        readerStore.setCurrentPage(1);
      }
      
      // Update URL if needed
      if (!chapterId.value || !pageParam.value) {
        updateUrl();
      }
    }
  } catch (error) {
    console.error('Failed to load content:', error);
  }
};

const updateUrl = () => {
  if (!chapter.value) return;
  
  router.replace({
    name: 'manga-reader',
    params: {
      id: mangaId.value,
      chapter: chapter.value.id,
      page: currentPage.value,
    },
  });
};

const nextPage = () => {
  readerStore.nextPage();
  updateUrl();
};

const prevPage = () => {
  readerStore.prevPage();
  updateUrl();
};

const nextChapter = async () => {
  await readerStore.loadNextChapter();
  updateUrl();
};

const prevChapter = async () => {
  await readerStore.loadPrevChapter();
  updateUrl();
};

const selectChapter = async (id) => {
  showChapterSelector.value = false;
  
  if (id === chapter.value?.id) return;
  
  await readerStore.fetchChapter(mangaId.value, id);
  await readerStore.fetchPages(mangaId.value, id);
  readerStore.setCurrentPage(1);
  updateUrl();
};

const updateSettings = (newSettings) => {
  readerStore.updateSettings(newSettings);
};

const toggleSettings = () => {
  showSettings.value = !showSettings.value;
};

const handleContentClick = (event) => {
  // Ignore clicks on controls
  if (event.target.closest('.reader-controls')) return;
  
  // Toggle controls visibility
  showControls.value = !showControls.value;
};

const handleMouseMove = () => {
  // Show controls on mouse move
  showControls.value = true;
  
  // Reset timeout
  if (controlsTimeout.value) {
    clearTimeout(controlsTimeout.value);
  }
  
  // Hide controls after 3 seconds of inactivity
  controlsTimeout.value = setTimeout(() => {
    showControls.value = false;
  }, 3000);
};

// Keyboard navigation
const handleKeyDown = (event) => {
  switch (event.key) {
    case 'ArrowRight':
      if (settings.value.readingDirection === 'ltr') {
        nextPage();
      } else {
        prevPage();
      }
      break;
    case 'ArrowLeft':
      if (settings.value.readingDirection === 'ltr') {
        prevPage();
      } else {
        nextPage();
      }
      break;
    case 'ArrowUp':
      prevChapter();
      break;
    case 'ArrowDown':
      nextChapter();
      break;
    case 'Escape':
      showControls.value = true;
      showSettings.value = false;
      showChapterSelector.value = false;
      break;
  }
};

// List view methods
const handleListScroll = () => {
  if (settings.value.pageLayout !== 'list') return;

  // Update current page based on scroll position
  const container = document.querySelector('.reader-list-container');
  if (!container) return;

  const scrollTop = container.scrollTop;
  const containerHeight = container.clientHeight;
  const scrollCenter = scrollTop + containerHeight / 2;

  // Find which page is in the center of the viewport
  for (let i = 1; i <= pages.value.length; i++) {
    const pageElement = document.getElementById(`list-page-${i}`);
    if (pageElement) {
      const pageTop = pageElement.offsetTop;
      const pageBottom = pageTop + pageElement.offsetHeight;

      if (scrollCenter >= pageTop && scrollCenter <= pageBottom) {
        currentPageInView.value = i;
        readerStore.setCurrentPage(i);
        break;
      }
    }
  }
};

const handleImageLoad = (pageNumber) => {
  // Update intersection observer when images load
  if (settings.value.pageLayout === 'list') {
    setupPageIntersectionObserver();
  }
};

const setupPageIntersectionObserver = () => {
  if (pageIntersectionObserver.value) {
    pageIntersectionObserver.value.disconnect();
  }

  pageIntersectionObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
          const pageNumber = parseInt(entry.target.id.replace('list-page-', ''));
          currentPageInView.value = pageNumber;
          readerStore.setCurrentPage(pageNumber);
        }
      });
    },
    {
      threshold: [0.5],
      rootMargin: '-20% 0px -20% 0px'
    }
  );

  // Observe all page elements
  pages.value.forEach((_, index) => {
    const pageElement = document.getElementById(`list-page-${index + 1}`);
    if (pageElement) {
      pageIntersectionObserver.value.observe(pageElement);
    }
  });
};

// Watch for route changes
watch([mangaId, chapterId], () => {
  loadContent();
});

// Watch for page layout changes to setup/cleanup observers
watch(() => settings.value.pageLayout, (newLayout, oldLayout) => {
  if (newLayout === 'list' && oldLayout !== 'list') {
    // Setup intersection observer for list view
    setTimeout(setupPageIntersectionObserver, 100);
  } else if (oldLayout === 'list' && newLayout !== 'list') {
    // Cleanup intersection observer
    if (pageIntersectionObserver.value) {
      pageIntersectionObserver.value.disconnect();
    }
  }
});

// Lifecycle hooks
onMounted(() => {
  loadContent();
  document.addEventListener('keydown', handleKeyDown);
  
  // Initial timeout to hide controls
  controlsTimeout.value = setTimeout(() => {
    showControls.value = false;
  }, 3000);
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyDown);

  if (controlsTimeout.value) {
    clearTimeout(controlsTimeout.value);
  }

  if (pageIntersectionObserver.value) {
    pageIntersectionObserver.value.disconnect();
  }
});
</script>

<style scoped>
.reader-content {
  cursor: pointer;
}

.reader-page-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.reader-page-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.reader-page-container::-webkit-scrollbar-track {
  background: transparent;
}

.reader-page-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.reader-fit-width .reader-page {
  width: 100%;
  height: auto;
}

.reader-fit-height .reader-page {
  width: auto;
  height: 100vh;
}

.reader-fit-both .reader-page {
  max-width: 100%;
  max-height: 100vh;
}

.reader-list-container {
  height: 100vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  scroll-behavior: smooth;
}

.reader-list-container::-webkit-scrollbar {
  width: 8px;
}

.reader-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.reader-list-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.list-page {
  display: block;
  margin: 0 auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.reader-fit-width .list-page {
  width: 100%;
  height: auto;
}
</style>
