import { defineStore } from "pinia";
import api from "../services/api";

export const useReaderStore = defineStore("reader", {
  state: () => ({
    manga: null,
    chapter: null,
    chapters: [],
    pages: [],
    currentPage: 1,
    loading: false,
    error: null,
    preloadedImages: new Map(), // Cache for preloaded images
    preloadQueue: [], // Queue of images being preloaded
    settings: {
      readingDirection: localStorage.getItem("readingDirection") || "rtl", // rtl, ltr
      pageLayout: localStorage.getItem("pageLayout") || "single", // single, double, list, adaptive
      fitMode: localStorage.getItem("fitMode") || "width", // width, height, both, original
      showPageNumbers: localStorage.getItem("showPageNumbers") === "true",
      autoAdvance: localStorage.getItem("autoAdvance") === "true",
      preloadDistance: parseInt(localStorage.getItem("preloadDistance")) || 3, // number of pages to preload
      imageQuality: localStorage.getItem("imageQuality") || "high", // high, medium, low
      adaptiveMode: localStorage.getItem("adaptiveMode") === "true", // auto-detect content type
    },
  }),

  getters: {
    getManga: (state) => state.manga,
    getChapter: (state) => state.chapter,
    getChapters: (state) => state.chapters,
    getPages: (state) => state.pages,
    getCurrentPage: (state) => state.currentPage,
    getSettings: (state) => state.settings,
    getTotalPages: (state) => state.pages.length,
    hasNextPage: (state) => state.currentPage < state.pages.length,
    hasPrevPage: (state) => state.currentPage > 1,

    // Double-page mode getters
    getCurrentPagePair: (state) => {
      if (state.settings.pageLayout !== 'double' || !state.pages.length) {
        return [state.pages[state.currentPage - 1]];
      }

      const leftPage = state.pages[state.currentPage - 1];
      const rightPage = state.pages[state.currentPage];

      // For RTL reading, swap the pages
      if (state.settings.readingDirection === 'rtl') {
        return rightPage ? [rightPage, leftPage] : [leftPage];
      } else {
        return rightPage ? [leftPage, rightPage] : [leftPage];
      }
    },

    getEffectivePageCount: (state) => {
      if (state.settings.pageLayout === 'double') {
        // In double-page mode, count page pairs
        return Math.ceil(state.pages.length / 2);
      }
      return state.pages.length;
    },

    getCurrentPageDisplay: (state, getters) => {
      if (state.settings.pageLayout === 'double') {
        const pairIndex = Math.ceil(state.currentPage / 2);
        return `${pairIndex} / ${getters.getEffectivePageCount}`;
      }
      return `${state.currentPage} / ${state.pages.length}`;
    },
    hasNextChapter: (state) => {
      if (!state.chapters.length || !state.chapter) return false;
      const currentIndex = state.chapters.findIndex(
        (c) => c.id === state.chapter.id,
      );
      return currentIndex < state.chapters.length - 1;
    },
    hasPrevChapter: (state) => {
      if (!state.chapters.length || !state.chapter) return false;
      const currentIndex = state.chapters.findIndex(
        (c) => c.id === state.chapter.id,
      );
      return currentIndex > 0;
    },
  },

  actions: {
    async fetchManga(mangaId) {
      this.loading = true;
      this.error = null;

      try {
        const response = await api.get(`/v1/manga/${mangaId}`);
        this.manga = response.data;
        return response.data;
      } catch (error) {
        this.error = error.response?.data?.detail || "Failed to fetch manga";
        console.error("Manga fetch error:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchChapters(mangaId) {
      this.loading = true;
      this.error = null;

      try {
        const response = await api.get(`/v1/manga/${mangaId}/chapters`);
        this.chapters = response.data;
        return response.data;
      } catch (error) {
        this.error = error.response?.data?.detail || "Failed to fetch chapters";
        console.error("Chapters fetch error:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchChapter(mangaId, chapterId) {
      this.loading = true;
      this.error = null;

      try {
        const response = await api.get(
          `/v1/manga/${mangaId}/chapters/${chapterId}`,
        );
        this.chapter = response.data;
        return response.data;
      } catch (error) {
        this.error = error.response?.data?.detail || "Failed to fetch chapter";
        console.error("Chapter fetch error:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchPages(mangaId, chapterId) {
      this.loading = true;
      this.error = null;

      try {
        const response = await api.get(
          `/v1/manga/${mangaId}/chapters/${chapterId}/pages`,
        );
        this.pages = response.data;

        // Trigger adaptive mode analysis if enabled
        if (this.settings.pageLayout === 'adaptive') {
          setTimeout(() => this.analyzeContentType(), 100);
        }

        return response.data;
      } catch (error) {
        this.error = error.response?.data?.detail || "Failed to fetch pages";
        console.error("Pages fetch error:", error);
      } finally {
        this.loading = false;
      }
    },

    async updateReadingProgress(mangaId, chapterId, page) {
      try {
        await api.post(`/v1/library/${mangaId}/progress`, {
          chapter_id: chapterId,
          page,
        });
      } catch (error) {
        console.error("Failed to update reading progress:", error);
      }
    },

    setCurrentPage(page) {
      this.currentPage = page;
      if (this.manga && this.chapter) {
        this.updateReadingProgress(this.manga.id, this.chapter.id, page);
      }

      // Trigger image preloading
      this.preloadImages();
    },

    nextPage() {
      const increment = this.settings.pageLayout === 'double' ? 2 : 1;

      if (this.currentPage + increment <= this.pages.length) {
        this.currentPage += increment;
      } else if (this.hasNextChapter && this.settings.autoAdvance) {
        this.loadNextChapter();
      }
    },

    prevPage() {
      const decrement = this.settings.pageLayout === 'double' ? 2 : 1;

      if (this.currentPage - decrement >= 1) {
        this.currentPage -= decrement;
      } else if (this.hasPrevChapter && this.settings.autoAdvance) {
        this.loadPrevChapter();
      }
    },

    async loadNextChapter() {
      if (!this.hasNextChapter) return;

      const currentIndex = this.chapters.findIndex(
        (c) => c.id === this.chapter.id,
      );
      const nextChapter = this.chapters[currentIndex + 1];

      await this.fetchChapter(this.manga.id, nextChapter.id);
      await this.fetchPages(this.manga.id, nextChapter.id);
      this.currentPage = 1;
    },

    async loadPrevChapter() {
      if (!this.hasPrevChapter) return;

      const currentIndex = this.chapters.findIndex(
        (c) => c.id === this.chapter.id,
      );
      const prevChapter = this.chapters[currentIndex - 1];

      await this.fetchChapter(this.manga.id, prevChapter.id);
      await this.fetchPages(this.manga.id, prevChapter.id);
      this.currentPage = this.pages.length;
    },

    updateSettings(settings) {
      this.settings = { ...this.settings, ...settings };

      // Save settings to localStorage
      Object.entries(this.settings).forEach(([key, value]) => {
        localStorage.setItem(key, value.toString());
      });
    },

    // Adaptive mode detection
    async analyzeContentType() {
      if (!this.pages.length || this.settings.pageLayout !== 'adaptive') return;

      try {
        const sampleSize = Math.min(5, this.pages.length); // Analyze first 5 pages
        const imageAnalyses = [];

        for (let i = 0; i < sampleSize; i++) {
          const analysis = await this.analyzeImage(this.pages[i].url);
          if (analysis) {
            imageAnalyses.push(analysis);
          }
        }

        if (imageAnalyses.length === 0) return;

        // Calculate average dimensions and aspect ratios
        const avgWidth = imageAnalyses.reduce((sum, img) => sum + img.width, 0) / imageAnalyses.length;
        const avgHeight = imageAnalyses.reduce((sum, img) => sum + img.height, 0) / imageAnalyses.length;
        const avgAspectRatio = avgWidth / avgHeight;

        // Determine optimal reading mode
        let detectedMode = 'single';

        // Check for long-strip content (webtoons)
        const tallImages = imageAnalyses.filter(img => img.height / img.width > 2).length;
        if (tallImages / imageAnalyses.length > 0.6) {
          detectedMode = 'list';
        }
        // Check for wide images that might benefit from double-page
        else if (avgAspectRatio > 1.4 && avgWidth > 1200) {
          detectedMode = 'double';
        }
        // Check if images are consistently wide enough for double-page
        else if (imageAnalyses.filter(img => img.width > img.height * 1.3).length / imageAnalyses.length > 0.7) {
          detectedMode = 'double';
        }

        // Apply detected mode
        if (detectedMode !== 'adaptive') {
          this.settings.pageLayout = detectedMode;
          localStorage.setItem('pageLayout', detectedMode);
          console.log(`Adaptive mode detected: ${detectedMode}`);
        }
      } catch (error) {
        console.error('Error analyzing content type:', error);
      }
    },

    analyzeImage(url) {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.naturalWidth,
            height: img.naturalHeight,
            aspectRatio: img.naturalWidth / img.naturalHeight
          });
        };
        img.onerror = () => resolve(null);
        img.src = url;
      });
    },

    // Image preloading system
    preloadImages() {
      if (!this.pages.length) return;

      const distance = this.settings.preloadDistance;
      const startIndex = Math.max(0, this.currentPage - 1);
      const endIndex = Math.min(this.pages.length - 1, startIndex + distance);

      // Clear old preloaded images that are too far away
      this.cleanupPreloadedImages();

      // Preload images in range
      for (let i = startIndex; i <= endIndex; i++) {
        const page = this.pages[i];
        if (page && !this.preloadedImages.has(page.url)) {
          this.preloadImage(page.url);
        }
      }
    },

    preloadImage(url) {
      if (this.preloadedImages.has(url) || this.preloadQueue.includes(url)) {
        return;
      }

      this.preloadQueue.push(url);

      const img = new Image();
      img.onload = () => {
        this.preloadedImages.set(url, img);
        this.preloadQueue = this.preloadQueue.filter(u => u !== url);
      };
      img.onerror = () => {
        this.preloadQueue = this.preloadQueue.filter(u => u !== url);
      };
      img.src = url;
    },

    cleanupPreloadedImages() {
      const distance = this.settings.preloadDistance;
      const currentIndex = this.currentPage - 1;
      const keepStart = Math.max(0, currentIndex - distance);
      const keepEnd = Math.min(this.pages.length - 1, currentIndex + distance * 2);

      // Remove images that are too far from current position
      for (const [url] of this.preloadedImages) {
        const pageIndex = this.pages.findIndex(p => p.url === url);
        if (pageIndex < keepStart || pageIndex > keepEnd) {
          this.preloadedImages.delete(url);
        }
      }
    },

    isImagePreloaded(url) {
      return this.preloadedImages.has(url);
    },
  },
});
