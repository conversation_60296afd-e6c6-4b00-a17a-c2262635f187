import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import { useReaderStore } from '../reader';

// Mock API
vi.mock('../../services/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
  },
}));

describe('Reader Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  describe('Settings Management', () => {
    it('should initialize with default settings', () => {
      const store = useReaderStore();
      
      expect(store.settings.readingDirection).toBe('rtl');
      expect(store.settings.pageLayout).toBe('single');
      expect(store.settings.fitMode).toBe('width');
      expect(store.settings.preloadDistance).toBe(3);
      expect(store.settings.imageQuality).toBe('high');
    });

    it('should update settings and persist to localStorage', () => {
      const store = useReaderStore();
      const mockSetItem = vi.spyOn(Storage.prototype, 'setItem');
      
      store.updateSettings({ pageLayout: 'double', fitMode: 'height' });
      
      expect(store.settings.pageLayout).toBe('double');
      expect(store.settings.fitMode).toBe('height');
      expect(mockSetItem).toHaveBeenCalledWith('pageLayout', 'double');
      expect(mockSetItem).toHaveBeenCalledWith('fitMode', 'height');
    });
  });

  describe('Double-Page Mode', () => {
    it('should return single page for single page mode', () => {
      const store = useReaderStore();
      store.pages = [
        { id: 1, url: 'page1.jpg' },
        { id: 2, url: 'page2.jpg' },
      ];
      store.currentPage = 1;
      store.settings.pageLayout = 'single';
      
      const pagePair = store.getCurrentPagePair;
      expect(pagePair).toHaveLength(1);
    });

    it('should return page pair for double page mode', () => {
      const store = useReaderStore();
      store.pages = [
        { id: 1, url: 'page1.jpg' },
        { id: 2, url: 'page2.jpg' },
        { id: 3, url: 'page3.jpg' },
      ];
      store.currentPage = 1;
      store.settings.pageLayout = 'double';
      
      const pagePair = store.getCurrentPagePair;
      expect(pagePair).toHaveLength(2);
    });

    it('should handle RTL reading direction correctly', () => {
      const store = useReaderStore();
      store.pages = [
        { id: 1, url: 'page1.jpg' },
        { id: 2, url: 'page2.jpg' },
      ];
      store.currentPage = 1;
      store.settings.pageLayout = 'double';
      store.settings.readingDirection = 'rtl';
      
      const pagePair = store.getCurrentPagePair;
      expect(pagePair[0].id).toBe(2); // Right page first in RTL
      expect(pagePair[1].id).toBe(1); // Left page second in RTL
    });
  });

  describe('Navigation', () => {
    beforeEach(() => {
      const store = useReaderStore();
      store.pages = [
        { id: 1, url: 'page1.jpg' },
        { id: 2, url: 'page2.jpg' },
        { id: 3, url: 'page3.jpg' },
        { id: 4, url: 'page4.jpg' },
      ];
      store.currentPage = 1;
    });

    it('should navigate correctly in single page mode', () => {
      const store = useReaderStore();
      store.settings.pageLayout = 'single';
      
      store.nextPage();
      expect(store.currentPage).toBe(2);
      
      store.prevPage();
      expect(store.currentPage).toBe(1);
    });

    it('should navigate correctly in double page mode', () => {
      const store = useReaderStore();
      store.settings.pageLayout = 'double';
      
      store.nextPage();
      expect(store.currentPage).toBe(3);
      
      store.prevPage();
      expect(store.currentPage).toBe(1);
    });
  });

  describe('Image Quality', () => {
    it('should return original URL for high quality', () => {
      const store = useReaderStore();
      store.pages = [{ id: 1, url: 'https://example.com/page1.jpg' }];
      store.currentPage = 1;
      store.settings.imageQuality = 'high';
      
      const url = store.getCurrentPageUrl;
      expect(url).toBe('https://example.com/page1.jpg');
    });

    it('should add quality parameters for medium quality', () => {
      const store = useReaderStore();
      store.pages = [{ id: 1, url: 'https://example.com/page1.jpg?test=1' }];
      store.currentPage = 1;
      store.settings.imageQuality = 'medium';
      
      const url = store.getCurrentPageUrl;
      expect(url).toContain('quality=75');
      expect(url).toContain('width=1200');
    });
  });

  describe('Preloading', () => {
    it('should preload images within distance', () => {
      const store = useReaderStore();
      store.pages = [
        { id: 1, url: 'page1.jpg' },
        { id: 2, url: 'page2.jpg' },
        { id: 3, url: 'page3.jpg' },
        { id: 4, url: 'page4.jpg' },
        { id: 5, url: 'page5.jpg' },
      ];
      store.currentPage = 2;
      store.settings.preloadDistance = 2;
      
      // Mock Image constructor
      global.Image = vi.fn(() => ({
        onload: null,
        onerror: null,
        src: '',
      }));
      
      store.preloadImages();
      
      // Should preload pages 2, 3, 4 (current + distance)
      expect(store.preloadQueue).toContain('page2.jpg');
      expect(store.preloadQueue).toContain('page3.jpg');
      expect(store.preloadQueue).toContain('page4.jpg');
    });
  });
});
